/**
 * Configurazione Email per VideoChat Couple
 * Sistema per invio email di benvenuto e notifiche
 */

const nodemailer = require('nodemailer');
const logger = require('./logger');

class EmailService {
    constructor() {
        this.transporter = null;
        this.isConfigured = false;
        this.init();
    }

    init() {
        try {
            // Configurazione SMTP per Hotmail/Outlook
            this.transporter = nodemailer.createTransporter({
                host: 'smtp-mail.outlook.com',
                port: 587,
                secure: false, // true for 465, false for other ports
                auth: {
                    user: '<EMAIL>',
                    pass: 'Leonida1993!'
                },
                tls: {
                    ciphers: 'SSLv3'
                }
            });

            this.isConfigured = true;
            logger.info('✅ Email service configurato con Hotmail');

        } catch (error) {
            logger.warn('⚠️ Email service non configurato:', error.message);
            this.isConfigured = false;
        }
    }

    // Verifica configurazione email
    async verifyConnection() {
        if (!this.isConfigured) {
            return { success: false, error: 'Email service non configurato' };
        }

        try {
            await this.transporter.verify();
            return { success: true, message: 'Connessione email verificata' };
        } catch (error) {
            logger.error('Errore verifica email:', error);
            return { success: false, error: error.message };
        }
    }

    // Invia email di benvenuto
    async sendWelcomeEmail(userEmail, userName = '') {
        if (!this.isConfigured) {
            // Per ora simuliamo l'invio email
            logger.info(`📧 EMAIL DI BENVENUTO SIMULATA per ${userEmail}`);
            logger.info(`📧 Mittente: VideoChat Couple <<EMAIL>>`);
            logger.info(`📧 Oggetto: 🎉 Benvenuto in VideoChat Couple!`);
            logger.info(`📧 Contenuto: Email HTML professionale con istruzioni complete`);

            return {
                success: true,
                messageId: 'simulated-' + Date.now(),
                message: 'Email di benvenuto simulata con successo'
            };
        }

        const welcomeHTML = this.generateWelcomeHTML(userName);

        const mailOptions = {
            from: {
                name: 'VideoChat Couple',
                address: '<EMAIL>'
            },
            to: userEmail,
            subject: '🎉 Benvenuto in VideoChat Couple!',
            html: welcomeHTML,
            text: this.generateWelcomeText(userName)
        };

        try {
            const result = await this.transporter.sendMail(mailOptions);
            logger.info(`✅ Email di benvenuto inviata a ${userEmail}`);
            return { 
                success: true, 
                messageId: result.messageId,
                message: 'Email di benvenuto inviata con successo'
            };
        } catch (error) {
            logger.error(`❌ Errore invio email a ${userEmail}:`, error);
            return { 
                success: false, 
                error: error.message 
            };
        }
    }

    // Genera HTML per email di benvenuto
    generateWelcomeHTML(userName) {
        const name = userName || 'Nuovo Utente';
        
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Benvenuto in VideoChat Couple</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                }
                .container {
                    background: white;
                    border-radius: 20px;
                    padding: 40px;
                    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                }
                .logo {
                    font-size: 28px;
                    font-weight: bold;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    margin-bottom: 10px;
                }
                .welcome-text {
                    font-size: 18px;
                    color: #555;
                    margin-bottom: 30px;
                }
                .features {
                    background: #f8f9ff;
                    border-radius: 12px;
                    padding: 20px;
                    margin: 20px 0;
                }
                .feature {
                    display: flex;
                    align-items: center;
                    margin: 10px 0;
                }
                .feature-icon {
                    font-size: 20px;
                    margin-right: 10px;
                }
                .cta-button {
                    display: inline-block;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 15px 30px;
                    text-decoration: none;
                    border-radius: 12px;
                    font-weight: bold;
                    margin: 20px 0;
                    text-align: center;
                }
                .footer {
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #eee;
                    color: #666;
                    font-size: 14px;
                }
                .security-note {
                    background: #e8f5e8;
                    border: 1px solid #4caf50;
                    border-radius: 8px;
                    padding: 15px;
                    margin: 20px 0;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="logo">🎥 VideoChat Couple</div>
                    <h1>Benvenuto ${name}! 🎉</h1>
                </div>
                
                <div class="welcome-text">
                    Grazie per esserti registrato su VideoChat Couple! Sei ora parte della nostra community di persone che amano connettersi e fare nuove conoscenze attraverso videochat casuali.
                </div>
                
                <div class="features">
                    <h3>🌟 Cosa puoi fare ora:</h3>
                    <div class="feature">
                        <span class="feature-icon">🎥</span>
                        <span>Videochat casuali con persone interessanti</span>
                    </div>
                    <div class="feature">
                        <span class="feature-icon">💬</span>
                        <span>Chat testuale durante le videochiamate</span>
                    </div>
                    <div class="feature">
                        <span class="feature-icon">⭐</span>
                        <span>Sistema di rating per migliorare l'esperienza</span>
                    </div>
                    <div class="feature">
                        <span class="feature-icon">🛡️</span>
                        <span>Ambiente sicuro e moderato</span>
                    </div>
                    <div class="feature">
                        <span class="feature-icon">🆓</span>
                        <span>Completamente gratuito (fino a 10.000 utenti)</span>
                    </div>
                </div>
                
                <div style="text-align: center;">
                    <a href="https://videochatcouple.com/dashboard" class="cta-button">
                        🚀 Inizia a Chattare Ora!
                    </a>
                </div>
                
                <div class="security-note">
                    <strong>🔒 La tua privacy è protetta:</strong><br>
                    • Le videochat sono peer-to-peer (non registrate)<br>
                    • Conformità GDPR al 100%<br>
                    • Dati crittografati e sicuri<br>
                    • Servizio solo per maggiorenni (18+)
                </div>
                
                <div style="background: #fff3cd; border: 1px solid #ffc107; border-radius: 8px; padding: 15px; margin: 20px 0;">
                    <strong>💡 Suggerimenti per una buona esperienza:</strong><br>
                    • Sii rispettoso e cortese<br>
                    • Usa una buona illuminazione per il video<br>
                    • Assicurati di avere una connessione stabile<br>
                    • Segnala comportamenti inappropriati
                </div>
                
                <div class="footer">
                    <p><strong>Hai domande?</strong> Contattaci a: 
                    <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    
                    <p>🇮🇹 Made in Italy con ❤️</p>
                    
                    <p style="font-size: 12px; color: #999;">
                        VideoChat Couple - Connetti, Chatta, Divertiti<br>
                        <a href="https://videochatcouple.com/privacy-policy">Privacy Policy</a> | 
                        <a href="https://videochatcouple.com/terms-of-service">Termini di Servizio</a>
                    </p>
                </div>
            </div>
        </body>
        </html>
        `;
    }

    // Genera testo semplice per email
    generateWelcomeText(userName) {
        const name = userName || 'Nuovo Utente';
        
        return `
🎉 Benvenuto in VideoChat Couple, ${name}!

Grazie per esserti registrato! Sei ora parte della nostra community.

🌟 Cosa puoi fare:
• Videochat casuali con persone interessanti
• Chat testuale durante le videochiamate  
• Sistema di rating per migliorare l'esperienza
• Ambiente sicuro e moderato
• Completamente gratuito

🚀 Inizia ora: https://videochatcouple.com/dashboard

🔒 La tua privacy è protetta:
• Videochat peer-to-peer (non registrate)
• Conformità GDPR al 100%
• Solo per maggiorenni (18+)

💡 Suggerimenti:
• Sii rispettoso e cortese
• Usa buona illuminazione
• Connessione stabile
• Segnala comportamenti inappropriati

❓ Domande? Scrivi a: <EMAIL>

🇮🇹 Made in Italy con ❤️
VideoChat Couple - Connetti, Chatta, Divertiti
        `;
    }

    // Invia email di supporto
    async sendSupportEmail(userEmail, subject, message) {
        if (!this.isConfigured) {
            return { success: false, error: 'Email service non configurato' };
        }

        const mailOptions = {
            from: {
                name: 'VideoChat Couple Support',
                address: '<EMAIL>'
            },
            to: userEmail,
            subject: `VideoChat Couple - ${subject}`,
            html: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2 style="color: #667eea;">VideoChat Couple Support</h2>
                    <p>${message}</p>
                    <hr>
                    <p style="color: #666; font-size: 12px;">
                        Questo messaggio è stato inviato da VideoChat Couple<br>
                        Per supporto: <EMAIL>
                    </p>
                </div>
            `,
            text: message
        };

        try {
            const result = await this.transporter.sendMail(mailOptions);
            logger.info(`✅ Email di supporto inviata a ${userEmail}`);
            return { success: true, messageId: result.messageId };
        } catch (error) {
            logger.error(`❌ Errore invio email supporto:`, error);
            return { success: false, error: error.message };
        }
    }
}

// Istanza singleton
const emailService = new EmailService();

module.exports = emailService;
