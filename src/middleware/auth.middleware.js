const jwt = require('jsonwebtoken');
const User = require('../models/user.model');

const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'Token richiesto'
      });
    }

    const token = authHeader.split(' ')[1];
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'videochat-secret-key');
    const user = await User.findById(decoded.id);

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Utente non trovato'
      });
    }

    req.user = {
      id: user._id,
      email: user.email
    };

    next();
  } catch (error) {
    res.status(401).json({
      success: false,
      message: 'Token non valido'
    });
  }
};

module.exports = {
  authenticateToken
};
