/**
 * Utility per la sanitizzazione e validazione input
 * Miglioramenti di sicurezza senza rompere funzionalità esistenti
 */

/**
 * Sanitizza un messaggio di chat
 * @param {string} message - Il messaggio da sanitizzare
 * @returns {string} - Il messaggio sanitizzato
 */
const sanitizeChatMessage = (message) => {
  if (!message || typeof message !== 'string') {
    return '';
  }

  // Rimuovi caratteri pericolosi ma mantieni funzionalità base
  return message
    .trim()
    .slice(0, 500) // <PERSON>ita lunghezza a 500 caratteri
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Rimuovi script
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '') // Rimuovi iframe
    .replace(/javascript:/gi, '') // Rimuovi javascript:
    .replace(/on\w+\s*=/gi, ''); // Rimuovi event handlers
};

/**
 * Valida un room ID
 * @param {string} roomId - L'ID della stanza
 * @returns {boolean} - True se valido
 */
const isValidRoomId = (roomId) => {
  if (!roomId || typeof roomId !== 'string') {
    return false;
  }
  
  // Formato: room_timestamp
  return /^room_\d+$/.test(roomId);
};

/**
 * Valida dati WebRTC
 * @param {object} data - I dati WebRTC
 * @returns {boolean} - True se validi
 */
const isValidWebRTCData = (data) => {
  if (!data || typeof data !== 'object') {
    return false;
  }

  // Verifica che abbia roomId valido
  if (!isValidRoomId(data.roomId)) {
    return false;
  }

  return true;
};

/**
 * Rate limiting semplice in memoria (fallback sicuro)
 */
class SimpleRateLimiter {
  constructor(maxRequests = 10, windowMs = 60000) {
    this.requests = new Map();
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }

  isAllowed(identifier) {
    const now = Date.now();
    const userRequests = this.requests.get(identifier) || [];
    
    // Rimuovi richieste vecchie
    const validRequests = userRequests.filter(time => now - time < this.windowMs);
    
    if (validRequests.length >= this.maxRequests) {
      return false;
    }

    // Aggiungi nuova richiesta
    validRequests.push(now);
    this.requests.set(identifier, validRequests);
    
    return true;
  }

  // Cleanup periodico per evitare memory leak
  cleanup() {
    const now = Date.now();
    for (const [identifier, requests] of this.requests.entries()) {
      const validRequests = requests.filter(time => now - time < this.windowMs);
      if (validRequests.length === 0) {
        this.requests.delete(identifier);
      } else {
        this.requests.set(identifier, validRequests);
      }
    }
  }
}

// Rate limiters per diverse azioni
const chatRateLimiter = new SimpleRateLimiter(20, 60000); // 20 messaggi per minuto
const matchRateLimiter = new SimpleRateLimiter(5, 30000);  // 5 match per 30 secondi
const webrtcRateLimiter = new SimpleRateLimiter(100, 60000); // 100 segnali WebRTC per minuto

// Cleanup ogni 5 minuti
setInterval(() => {
  chatRateLimiter.cleanup();
  matchRateLimiter.cleanup();
  webrtcRateLimiter.cleanup();
}, 5 * 60 * 1000);

module.exports = {
  sanitizeChatMessage,
  isValidRoomId,
  isValidWebRTCData,
  chatRateLimiter,
  matchRateLimiter,
  webrtcRateLimiter
};
