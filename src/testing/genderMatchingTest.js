/**
 * Test Sistema Accoppiamento per Genere
 * Simula 100 account misti e testa l'accoppiamento donna-uomo
 */

const User = require('../models/user.model');
const bcrypt = require('bcrypt');
const logger = require('../config/logger');

class GenderMatchingTester {
    constructor() {
        this.testUsers = [];
        this.testResults = {
            totalTests: 0,
            successfulMatches: 0,
            failedMatches: 0,
            genderDistribution: {
                male: 0,
                female: 0,
                other: 0
            },
            matchingResults: {
                maleToFemale: 0,
                femaleToMale: 0,
                sameGender: 0,
                otherCombinations: 0
            }
        };
    }

    // Genera utenti di test
    async generateTestUsers(count = 100) {
        console.log(`🧪 Generando ${count} utenti di test...`);
        
        const users = [];
        const genders = ['male', 'female', 'other'];
        const preferences = ['male', 'female', 'all'];
        
        for (let i = 0; i < count; i++) {
            const gender = genders[Math.floor(Math.random() * genders.length)];
            const preference = preferences[Math.floor(Math.random() * preferences.length)];
            
            // Distribuzione più realistica: 45% maschi, 45% femmine, 10% altro
            let actualGender;
            if (i < count * 0.45) {
                actualGender = 'male';
            } else if (i < count * 0.9) {
                actualGender = 'female';
            } else {
                actualGender = 'other';
            }
            
            const user = {
                email: `test${i}@testdomain.com`,
                password: await bcrypt.hash('testpass123', 12),
                gender: actualGender,
                preferences: {
                    gender: preference,
                    ageRange: { min: 18, max: 35 }
                },
                freeUser: true,
                isOnline: true,
                testUser: true // Flag per identificare utenti di test
            };
            
            users.push(user);
            this.testResults.genderDistribution[actualGender]++;
        }
        
        return users;
    }

    // Simula algoritmo di matching con preferenze genere
    simulateMatching(users) {
        console.log('🔄 Simulando algoritmo di matching...');
        
        const availableUsers = [...users];
        const matches = [];
        
        while (availableUsers.length >= 2) {
            // Prendi primo utente
            const user1 = availableUsers.shift();
            
            // Trova partner compatibile
            let partnerIndex = -1;
            
            for (let i = 0; i < availableUsers.length; i++) {
                const user2 = availableUsers[i];
                
                if (this.areCompatible(user1, user2)) {
                    partnerIndex = i;
                    break;
                }
            }
            
            if (partnerIndex !== -1) {
                const user2 = availableUsers.splice(partnerIndex, 1)[0];
                matches.push({ user1, user2 });
                this.testResults.successfulMatches++;
                
                // Traccia tipo di match
                this.trackMatchType(user1, user2);
            } else {
                this.testResults.failedMatches++;
            }
            
            this.testResults.totalTests++;
        }
        
        return matches;
    }

    // Verifica compatibilità tra due utenti
    areCompatible(user1, user2) {
        // Verifica preferenze di user1 verso user2
        const user1Compatible = this.checkGenderPreference(user1.preferences.gender, user2.gender);
        
        // Verifica preferenze di user2 verso user1
        const user2Compatible = this.checkGenderPreference(user2.preferences.gender, user1.gender);
        
        return user1Compatible && user2Compatible;
    }

    // Controlla se il genere target è compatibile con la preferenza
    checkGenderPreference(preference, targetGender) {
        if (preference === 'all') return true;
        return preference === targetGender;
    }

    // Traccia il tipo di match per statistiche
    trackMatchType(user1, user2) {
        if (user1.gender === 'male' && user2.gender === 'female') {
            this.testResults.matchingResults.maleToFemale++;
        } else if (user1.gender === 'female' && user2.gender === 'male') {
            this.testResults.matchingResults.femaleToMale++;
        } else if (user1.gender === user2.gender) {
            this.testResults.matchingResults.sameGender++;
        } else {
            this.testResults.matchingResults.otherCombinations++;
        }
    }

    // Esegue test completo
    async runFullTest(userCount = 100) {
        console.log('🚀 Avvio test completo accoppiamento genere...\n');
        
        try {
            // 1. Genera utenti di test
            const users = await this.generateTestUsers(userCount);
            console.log(`✅ Generati ${users.length} utenti di test`);
            console.log(`   👨 Maschi: ${this.testResults.genderDistribution.male}`);
            console.log(`   👩 Femmine: ${this.testResults.genderDistribution.female}`);
            console.log(`   🤷 Altri: ${this.testResults.genderDistribution.other}\n`);
            
            // 2. Simula matching
            const matches = this.simulateMatching(users);
            console.log(`✅ Completato matching simulation`);
            console.log(`   💑 Match riusciti: ${this.testResults.successfulMatches}`);
            console.log(`   ❌ Match falliti: ${this.testResults.failedMatches}\n`);
            
            // 3. Analizza risultati
            this.analyzeResults();
            
            // 4. Test specifici
            await this.runSpecificTests();
            
            return this.testResults;
            
        } catch (error) {
            console.error('❌ Errore durante il test:', error);
            throw error;
        }
    }

    // Analizza e mostra risultati dettagliati
    analyzeResults() {
        console.log('📊 ANALISI RISULTATI:\n');
        
        const successRate = (this.testResults.successfulMatches / this.testResults.totalTests * 100).toFixed(2);
        console.log(`🎯 Tasso di successo: ${successRate}%`);
        
        console.log('\n💕 Distribuzione match per genere:');
        console.log(`   👨➡️👩 Maschio → Femmina: ${this.testResults.matchingResults.maleToFemale}`);
        console.log(`   👩➡️👨 Femmina → Maschio: ${this.testResults.matchingResults.femaleToMale}`);
        console.log(`   👥 Stesso genere: ${this.testResults.matchingResults.sameGender}`);
        console.log(`   🤷 Altre combinazioni: ${this.testResults.matchingResults.otherCombinations}`);
        
        const totalHeteroMatches = this.testResults.matchingResults.maleToFemale + 
                                  this.testResults.matchingResults.femaleToMale;
        const heteroPercentage = (totalHeteroMatches / this.testResults.successfulMatches * 100).toFixed(2);
        
        console.log(`\n🌈 Match eterosessuali: ${totalHeteroMatches} (${heteroPercentage}%)`);
    }

    // Test specifici per scenari particolari
    async runSpecificTests() {
        console.log('\n🔬 ESECUZIONE TEST SPECIFICI:\n');
        
        // Test 1: Solo uomini che cercano donne
        console.log('Test 1: Uomini che cercano solo donne');
        const menSeekingWomen = await this.testSpecificScenario(
            { gender: 'male', preference: 'female' },
            { gender: 'female', preference: 'all' },
            10
        );
        console.log(`   Risultato: ${menSeekingWomen.matches}/${menSeekingWomen.attempts} match riusciti\n`);
        
        // Test 2: Solo donne che cercano uomini
        console.log('Test 2: Donne che cercano solo uomini');
        const womenSeekingMen = await this.testSpecificScenario(
            { gender: 'female', preference: 'male' },
            { gender: 'male', preference: 'all' },
            10
        );
        console.log(`   Risultato: ${womenSeekingMen.matches}/${womenSeekingMen.attempts} match riusciti\n`);
        
        // Test 3: Tutti cercano tutti
        console.log('Test 3: Tutti cercano tutti (preferenza "all")');
        const allSeekingAll = await this.testSpecificScenario(
            { gender: 'male', preference: 'all' },
            { gender: 'female', preference: 'all' },
            10
        );
        console.log(`   Risultato: ${allSeekingAll.matches}/${allSeekingAll.attempts} match riusciti\n`);
    }

    // Test scenario specifico
    async testSpecificScenario(user1Config, user2Config, pairs) {
        const users = [];
        
        // Crea coppie di utenti con configurazioni specifiche
        for (let i = 0; i < pairs; i++) {
            users.push({
                email: `scenario1_${i}@test.com`,
                gender: user1Config.gender,
                preferences: { gender: user1Config.preference }
            });
            
            users.push({
                email: `scenario2_${i}@test.com`,
                gender: user2Config.gender,
                preferences: { gender: user2Config.preference }
            });
        }
        
        const matches = this.simulateMatching(users);
        
        return {
            attempts: pairs,
            matches: matches.length
        };
    }

    // Pulisce utenti di test dal database
    async cleanupTestUsers() {
        try {
            const result = await User.deleteMany({ testUser: true });
            console.log(`🧹 Rimossi ${result.deletedCount} utenti di test dal database`);
        } catch (error) {
            console.error('❌ Errore durante cleanup:', error);
        }
    }

    // Genera report finale
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalUsers: this.testResults.genderDistribution.male + 
                           this.testResults.genderDistribution.female + 
                           this.testResults.genderDistribution.other,
                successRate: (this.testResults.successfulMatches / this.testResults.totalTests * 100).toFixed(2) + '%',
                heteroMatchRate: ((this.testResults.matchingResults.maleToFemale + 
                                  this.testResults.matchingResults.femaleToMale) / 
                                  this.testResults.successfulMatches * 100).toFixed(2) + '%'
            },
            details: this.testResults
        };
        
        return report;
    }
}

module.exports = GenderMatchingTester;
