/**
 * Test di Capacità Sistema VideoChat Couple
 * Determina il numero massimo di utenti gestibili
 */

require('dotenv').config();
const mongoose = require('mongoose');
const io = require('socket.io-client');
const logger = require('../config/logger');

class CapacityTester {
    constructor() {
        this.clients = [];
        this.results = {
            maxConcurrentUsers: 0,
            connectionTime: [],
            memoryUsage: [],
            responseTime: [],
            errors: [],
            successfulConnections: 0,
            failedConnections: 0
        };
        this.serverUrl = process.env.NODE_ENV === 'production' 
            ? 'https://videochatcouple.com' 
            : 'http://localhost:3001';
    }

    // Test connessioni simultanee
    async testConcurrentConnections(maxUsers = 100, step = 10) {
        console.log(`🧪 TEST CAPACITÀ: ${maxUsers} utenti massimi, step ${step}\n`);
        
        for (let userCount = step; userCount <= maxUsers; userCount += step) {
            console.log(`\n📊 Testing ${userCount} utenti simultanei...`);
            
            const startTime = Date.now();
            const startMemory = process.memoryUsage();
            
            try {
                // Crea connessioni
                await this.createConnections(userCount);
                
                // Misura performance
                const connectionTime = Date.now() - startTime;
                const endMemory = process.memoryUsage();
                
                // Test operazioni base
                const responseTime = await this.testBasicOperations();
                
                // Raccogli metriche server
                const serverMetrics = await this.getServerMetrics();
                
                // Salva risultati
                this.results.connectionTime.push({
                    users: userCount,
                    time: connectionTime,
                    avgPerUser: connectionTime / userCount
                });
                
                this.results.memoryUsage.push({
                    users: userCount,
                    heapUsed: (endMemory.heapUsed - startMemory.heapUsed) / 1024 / 1024,
                    rss: (endMemory.rss - startMemory.rss) / 1024 / 1024
                });
                
                this.results.responseTime.push({
                    users: userCount,
                    avgResponse: responseTime
                });
                
                console.log(`   ✅ Connessioni: ${this.results.successfulConnections}/${userCount}`);
                console.log(`   ⏱️  Tempo connessione: ${connectionTime}ms`);
                console.log(`   🧠 Memoria aggiunta: ${((endMemory.heapUsed - startMemory.heapUsed) / 1024 / 1024).toFixed(2)}MB`);
                console.log(`   📡 Tempo risposta: ${responseTime}ms`);
                
                if (serverMetrics) {
                    console.log(`   🖥️  Server CPU: ${serverMetrics.cpu || 'N/A'}`);
                    console.log(`   💾 Server Memory: ${serverMetrics.memory || 'N/A'}`);
                }
                
                // Verifica se il sistema è ancora stabile
                if (responseTime > 5000 || this.results.failedConnections > userCount * 0.1) {
                    console.log(`   ⚠️  Sistema instabile a ${userCount} utenti`);
                    break;
                }
                
                this.results.maxConcurrentUsers = userCount;
                
                // Pausa tra test
                await this.sleep(2000);
                
            } catch (error) {
                console.log(`   ❌ Errore a ${userCount} utenti:`, error.message);
                this.results.errors.push({
                    users: userCount,
                    error: error.message
                });
                break;
            } finally {
                // Cleanup connessioni
                await this.cleanupConnections();
            }
        }
        
        return this.results;
    }

    // Crea connessioni Socket.IO
    async createConnections(count) {
        const promises = [];
        this.results.successfulConnections = 0;
        this.results.failedConnections = 0;
        
        for (let i = 0; i < count; i++) {
            promises.push(this.createSingleConnection(i));
        }
        
        await Promise.allSettled(promises);
    }

    // Crea singola connessione
    createSingleConnection(index) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                this.results.failedConnections++;
                reject(new Error('Connection timeout'));
            }, 10000);
            
            try {
                const client = io(this.serverUrl, {
                    transports: ['websocket'],
                    timeout: 5000,
                    forceNew: true
                });
                
                client.on('connect', () => {
                    clearTimeout(timeout);
                    this.clients.push(client);
                    this.results.successfulConnections++;
                    resolve(client);
                });
                
                client.on('connect_error', (error) => {
                    clearTimeout(timeout);
                    this.results.failedConnections++;
                    reject(error);
                });
                
            } catch (error) {
                clearTimeout(timeout);
                this.results.failedConnections++;
                reject(error);
            }
        });
    }

    // Test operazioni base
    async testBasicOperations() {
        if (this.clients.length === 0) return 0;
        
        const startTime = Date.now();
        const promises = [];
        
        // Test su un campione di client (max 10 per non sovraccaricare)
        const sampleSize = Math.min(10, this.clients.length);
        const sampleClients = this.clients.slice(0, sampleSize);
        
        for (const client of sampleClients) {
            promises.push(this.testClientOperations(client));
        }
        
        await Promise.allSettled(promises);
        
        return (Date.now() - startTime) / sampleSize;
    }

    // Test operazioni singolo client
    testClientOperations(client) {
        return new Promise((resolve) => {
            const timeout = setTimeout(() => resolve(), 3000);
            
            // Simula autenticazione
            client.emit('authenticate', {
                token: 'test-token-' + Date.now()
            });
            
            // Ascolta risposta
            client.once('authenticated', () => {
                clearTimeout(timeout);
                resolve();
            });
            
            client.once('auth-error', () => {
                clearTimeout(timeout);
                resolve();
            });
        });
    }

    // Ottieni metriche server
    async getServerMetrics() {
        try {
            const response = await fetch(`${this.serverUrl}/api/stats`);
            if (response.ok) {
                const data = await response.json();
                return {
                    memory: data.memory?.heapUsed ? 
                        (data.memory.heapUsed / 1024 / 1024).toFixed(2) + 'MB' : null,
                    uptime: data.uptime,
                    activeUsers: data.activeUsers,
                    redis: data.redis?.storageType
                };
            }
        } catch (error) {
            // Ignora errori di fetch
        }
        return null;
    }

    // Cleanup connessioni
    async cleanupConnections() {
        for (const client of this.clients) {
            try {
                client.disconnect();
            } catch (error) {
                // Ignora errori di disconnessione
            }
        }
        this.clients = [];
        
        // Attendi che le connessioni si chiudano
        await this.sleep(1000);
    }

    // Utility sleep
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Genera report finale
    generateCapacityReport() {
        const report = {
            timestamp: new Date().toISOString(),
            maxConcurrentUsers: this.results.maxConcurrentUsers,
            summary: {
                recommendedCapacity: Math.floor(this.results.maxConcurrentUsers * 0.7), // 70% del massimo
                safeCapacity: Math.floor(this.results.maxConcurrentUsers * 0.5), // 50% del massimo
                peakCapacity: this.results.maxConcurrentUsers
            },
            performance: {
                avgConnectionTime: this.calculateAverage(this.results.connectionTime, 'time'),
                avgResponseTime: this.calculateAverage(this.results.responseTime, 'avgResponse'),
                memoryPerUser: this.calculateMemoryPerUser(),
                successRate: (this.results.successfulConnections / 
                            (this.results.successfulConnections + this.results.failedConnections) * 100).toFixed(2) + '%'
            },
            details: this.results
        };
        
        return report;
    }

    calculateAverage(array, field) {
        if (array.length === 0) return 0;
        const sum = array.reduce((acc, item) => acc + item[field], 0);
        return Math.round(sum / array.length);
    }

    calculateMemoryPerUser() {
        if (this.results.memoryUsage.length === 0) return 0;
        const lastEntry = this.results.memoryUsage[this.results.memoryUsage.length - 1];
        return (lastEntry.heapUsed / lastEntry.users).toFixed(2);
    }
}

// Esegui test se chiamato direttamente
if (require.main === module) {
    const tester = new CapacityTester();
    const maxUsers = parseInt(process.argv[2]) || 100;
    const step = parseInt(process.argv[3]) || 10;
    
    console.log('🚀 AVVIO TEST CAPACITÀ SISTEMA\n');
    console.log('='.repeat(50));
    
    tester.testConcurrentConnections(maxUsers, step)
        .then(() => {
            const report = tester.generateCapacityReport();
            
            console.log('\n' + '='.repeat(50));
            console.log('📋 REPORT CAPACITÀ FINALE\n');
            
            console.log(`🎯 Capacità massima testata: ${report.maxConcurrentUsers} utenti`);
            console.log(`✅ Capacità raccomandata: ${report.summary.recommendedCapacity} utenti (70%)`);
            console.log(`🛡️ Capacità sicura: ${report.summary.safeCapacity} utenti (50%)`);
            console.log(`⚡ Capacità picco: ${report.summary.peakCapacity} utenti (100%)`);
            
            console.log('\n📊 Performance:');
            console.log(`   ⏱️  Tempo connessione medio: ${report.performance.avgConnectionTime}ms`);
            console.log(`   📡 Tempo risposta medio: ${report.performance.avgResponseTime}ms`);
            console.log(`   🧠 Memoria per utente: ${report.performance.memoryPerUser}MB`);
            console.log(`   ✅ Tasso successo: ${report.performance.successRate}`);
            
            // Salva report
            const fs = require('fs');
            if (!fs.existsSync('./test-reports')) {
                fs.mkdirSync('./test-reports', { recursive: true });
            }
            
            const reportPath = `./test-reports/capacity-test-${Date.now()}.json`;
            fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
            console.log(`\n💾 Report salvato: ${reportPath}`);
            
            console.log('\n✅ TEST CAPACITÀ COMPLETATO!');
            process.exit(0);
            
        })
        .catch(error => {
            console.error('\n❌ ERRORE TEST CAPACITÀ:', error);
            process.exit(1);
        });
}

module.exports = CapacityTester;
