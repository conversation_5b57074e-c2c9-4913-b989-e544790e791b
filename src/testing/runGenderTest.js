/**
 * Script per eseguire test accoppiamento genere
 * Uso: node src/testing/runGenderTest.js [numero_utenti]
 */

require('dotenv').config();
const mongoose = require('mongoose');
const GenderMatchingTester = require('./genderMatchingTest');

async function runTest() {
    console.log('🧪 AVVIO TEST ACCOPPIAMENTO GENERE\n');
    console.log('=' .repeat(50));
    
    try {
        // Connetti al database
        console.log('🔌 Connessione al database...');
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('✅ Database connesso\n');
        
        // Numero di utenti da testare (default 100)
        const userCount = parseInt(process.argv[2]) || 100;
        console.log(`👥 Testando con ${userCount} utenti simulati\n`);
        
        // Crea tester
        const tester = new GenderMatchingTester();
        
        // Esegui test completo
        const results = await tester.runFullTest(userCount);
        
        // Genera report finale
        console.log('\n' + '='.repeat(50));
        console.log('📋 REPORT FINALE:\n');
        
        const report = tester.generateReport();
        console.log(`📅 Timestamp: ${report.timestamp}`);
        console.log(`👥 Utenti totali testati: ${report.summary.totalUsers}`);
        console.log(`🎯 Tasso successo matching: ${report.summary.successRate}`);
        console.log(`💕 Percentuale match eterosessuali: ${report.summary.heteroMatchRate}`);
        
        console.log('\n📊 DETTAGLI COMPLETI:');
        console.log(JSON.stringify(report.details, null, 2));
        
        // Salva report su file
        const fs = require('fs');
        const reportPath = `./test-reports/gender-matching-${Date.now()}.json`;
        
        // Crea directory se non esiste
        if (!fs.existsSync('./test-reports')) {
            fs.mkdirSync('./test-reports', { recursive: true });
        }
        
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n💾 Report salvato in: ${reportPath}`);
        
        // Cleanup (opzionale)
        console.log('\n🧹 Cleanup utenti di test...');
        await tester.cleanupTestUsers();
        
        console.log('\n✅ TEST COMPLETATO CON SUCCESSO!');
        
        // Valutazione risultati
        evaluateResults(report);
        
    } catch (error) {
        console.error('\n❌ ERRORE DURANTE IL TEST:', error);
        process.exit(1);
    } finally {
        await mongoose.disconnect();
        console.log('\n🔌 Database disconnesso');
        process.exit(0);
    }
}

function evaluateResults(report) {
    console.log('\n' + '='.repeat(50));
    console.log('🔍 VALUTAZIONE RISULTATI:\n');
    
    const successRate = parseFloat(report.summary.successRate);
    const heteroRate = parseFloat(report.summary.heteroMatchRate);
    
    // Valuta tasso di successo
    if (successRate >= 80) {
        console.log('✅ ECCELLENTE: Tasso di successo molto alto (≥80%)');
    } else if (successRate >= 60) {
        console.log('✅ BUONO: Tasso di successo accettabile (≥60%)');
    } else if (successRate >= 40) {
        console.log('⚠️  MEDIO: Tasso di successo migliorabile (≥40%)');
    } else {
        console.log('❌ BASSO: Tasso di successo insufficiente (<40%)');
    }
    
    // Valuta distribuzione genere
    if (heteroRate >= 70) {
        console.log('✅ OTTIMO: Buona distribuzione match eterosessuali');
    } else if (heteroRate >= 50) {
        console.log('✅ BUONO: Distribuzione match accettabile');
    } else {
        console.log('⚠️  ATTENZIONE: Bassa percentuale match eterosessuali');
    }
    
    // Raccomandazioni
    console.log('\n💡 RACCOMANDAZIONI:');
    
    if (successRate < 70) {
        console.log('- Considera di migliorare l\'algoritmo di matching');
        console.log('- Aggiungi più flessibilità nelle preferenze');
    }
    
    if (heteroRate < 60) {
        console.log('- Verifica la distribuzione delle preferenze genere');
        console.log('- Considera di bilanciare meglio i generi');
    }
    
    if (report.details.failedMatches > report.details.successfulMatches * 0.3) {
        console.log('- Troppi match falliti, rivedi la logica di compatibilità');
    }
    
    console.log('\n🎯 ALGORITMO DI MATCHING: ' + 
                (successRate >= 70 && heteroRate >= 60 ? 'FUNZIONA BENE' : 'NECESSITA MIGLIORAMENTI'));
}

// Esegui test se chiamato direttamente
if (require.main === module) {
    runTest();
}

module.exports = { runTest, evaluateResults };
