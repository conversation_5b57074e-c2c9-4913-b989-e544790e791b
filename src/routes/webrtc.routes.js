/**
 * Route per la configurazione WebRTC
 */
const express = require('express');
const config = require('../config/config');
const { authenticateToken } = require('../middleware/auth.middleware');

const router = express.Router();

/**
 * @route GET /api/webrtc/config
 * @desc Ottiene la configurazione WebRTC
 * @access Private
 */
router.get('/config', authenticateToken, (req, res) => {
  try {
    // Invia la configurazione WebRTC al client
    res.json({
      iceServers: config.webrtc.iceServers,
      iceCandidatePoolSize: config.webrtc.iceCandidatePoolSize,
      bundlePolicy: config.webrtc.bundlePolicy,
      rtcpMuxPolicy: config.webrtc.rtcpMuxPolicy,
      sdpSemantics: config.webrtc.sdpSemantics
    });
  } catch (error) {
    res.status(500).json({ message: 'Errore durante il recupero della configurazione WebRTC' });
  }
});

module.exports = router;
