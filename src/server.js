/**
 * VideoChat Couple - Server principale
 * Sistema completo con login, registrazione, videochat e gestione crediti
 * Versione organizzata con MongoDB Atlas
 */

require('dotenv').config();
const express = require('express');
const http = require('http');
const https = require('https');
const fs = require('fs');
const socketIo = require('socket.io');
const path = require('path');
const cors = require('cors');
const mongoose = require('mongoose');

// Import configurazioni
const { connectDatabase } = require('./config/database');
const { configureExpress } = require('./config/express');
const logger = require('./config/logger');

// Import routes
const authRoutes = require('./routes/auth.routes');
const userRoutes = require('./routes/user.routes');
const sessionRoutes = require('./routes/session.routes');

// Import models
const User = require('./models/user.model');

// Import utilities per sicurezza
const {
  sanitizeChatMessage,
  isValidRoomId,
  isValidWebRTCData,
  chatRateLimiter,
  matchRateLimiter,
  webrtcRateLimiter
} = require('./utils/sanitizer');

// Import Redis Manager con fallback sicuro
const redisManager = require('./services/redisManager');

// Import servizi avanzati
const feedbackSystem = require('./services/feedbackSystem');
const analytics = require('./services/analyticsService');

const app = express();

// Connessione MongoDB
connectDatabase();

// Configurazione Express
configureExpress(app);

// Configurazione server - HTTP per Nginx proxy
const server = http.createServer(app);
logger.info('🔗 Server HTTP configurato per proxy Nginx');

// Configurazione Socket.IO
const io = socketIo(server, {
  cors: {
    origin: "https://videochatcouple.com",
    methods: ["GET", "POST"],
    credentials: true
  }
});

// Database in memoria per sessioni attive (mantenuto come fallback)
const activeUsers = new Map();
const chatRooms = new Map();

// Helper functions per gestione utenti con Redis + fallback memoria
const userManager = {
  async addUser(socketId, userData) {
    // Salva in Redis (con fallback automatico)
    await redisManager.setUser(socketId, userData);
    // Mantieni anche in memoria per compatibilità
    activeUsers.set(socketId, userData);
  },

  async getUser(socketId) {
    // Prova prima Redis, poi memoria locale
    let user = await redisManager.getUser(socketId);
    if (!user) {
      user = activeUsers.get(socketId);
    }
    return user;
  },

  async removeUser(socketId) {
    // Rimuovi da entrambi
    await redisManager.removeUser(socketId);
    activeUsers.delete(socketId);
  },

  async getAllUsers() {
    // Prova Redis prima, poi fallback memoria
    try {
      const redisUsers = await redisManager.getAllActiveUsers();
      if (redisUsers.length > 0) {
        return redisUsers;
      }
    } catch (error) {
      logger.warn('Fallback to memory for getAllUsers');
    }

    // Fallback memoria
    return Array.from(activeUsers.entries()).map(([socketId, userData]) => ({
      socketId,
      userData
    }));
  }
};

const roomManager = {
  async addRoom(roomId, roomData) {
    await redisManager.setRoom(roomId, roomData);
    chatRooms.set(roomId, roomData);
  },

  async getRoom(roomId) {
    let room = await redisManager.getRoom(roomId);
    if (!room) {
      room = chatRooms.get(roomId);
    }
    return room;
  },

  async removeRoom(roomId) {
    await redisManager.removeRoom(roomId);
    chatRooms.delete(roomId);
  }
};

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET;

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/sessions', sessionRoutes);
app.use('/api/reports', require('./routes/report.routes'));
app.use('/api/support', require('./routes/support.routes'));
app.use('/api/webrtc', require('./routes/webrtc.routes'));

// Route principale per servire l'app
app.get('/dashboard', (req, res) => {
    res.sendFile(path.join(__dirname, '../public', 'dashboard-pro.html'));
});

app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../public', 'index.html'));
});

// Route per pagine legali
app.get('/privacy-policy', (req, res) => {
    res.sendFile(path.join(__dirname, '../public', 'privacy-policy.html'));
});

app.get('/terms-of-service', (req, res) => {
    res.sendFile(path.join(__dirname, '../public', 'terms-of-service.html'));
});

app.get('/cookie-policy', (req, res) => {
    res.sendFile(path.join(__dirname, '../public', 'cookie-policy.html'));
});

// Endpoint per statistiche sistema (solo per admin/monitoring)
app.get('/api/stats', (req, res) => {
    try {
        const stats = {
            server: 'VideoChat Couple',
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            redis: redisManager.getStats(),
            activeUsers: activeUsers.size,
            activeRooms: chatRooms.size,
            analytics: analytics.getStats(),
            feedback: feedbackSystem.getFeedbackStats(),
            timestamp: new Date().toISOString()
        };
        res.json(stats);
    } catch (error) {
        res.status(500).json({ error: 'Errore recupero statistiche' });
    }
});

// Endpoint per analytics dettagliate
app.get('/api/analytics', (req, res) => {
    try {
        const days = parseInt(req.query.days) || 7;
        const analyticsData = {
            current: analytics.getStats(),
            trends: analytics.getTrends(days)
        };
        res.json(analyticsData);
    } catch (error) {
        res.status(500).json({ error: 'Errore recupero analytics' });
    }
});

// Endpoint per inviare feedback
app.post('/api/feedback', async (req, res) => {
    try {
        const { roomId, rating, tags, comment } = req.body;
        const userId = req.user?.id; // Assumendo middleware auth

        if (!roomId || !rating || !userId) {
            return res.status(400).json({ error: 'Dati mancanti' });
        }

        const result = await feedbackSystem.submitFeedback(roomId, userId, {
            rating,
            tags,
            comment
        });

        res.json(result);
    } catch (error) {
        res.status(500).json({ error: 'Errore invio feedback' });
    }
});

// Gestione Socket.IO per videochat
io.on('connection', (socket) => {
  logger.info('Nuovo utente connesso:', socket.id);

  // Autenticazione socket
  socket.on('authenticate', async (token) => {
    try {
      const jwt = require('jsonwebtoken');
      const decoded = jwt.verify(token, JWT_SECRET);
      socket.userId = decoded.userId;
      socket.userEmail = decoded.email;

      const user = await User.findById(decoded.userId);
      if (user) {
        user.isOnline = true;
        await user.save();

        // Usa il nuovo userManager con Redis + fallback
        await userManager.addUser(socket.id, user);

        // Track analytics
        analytics.trackEvent('user_connected', {
          userId: user._id.toString(),
          email: user.email,
          socketId: socket.id
        });

        socket.emit('authenticated', {
          userId: user._id,
          email: user.email,
          freeUser: user.freeUser
        });

        // Notifica agli altri utenti
        socket.broadcast.emit('user-online', {
          userId: user._id,
          email: user.email
        });
      }
    } catch (error) {
      logger.error('Errore autenticazione socket:', error);
      socket.emit('auth-error', 'Token non valido');
    }
  });

  // Richiesta di ricerca partner (con rate limiting)
  socket.on('find-match', async (data) => {
    try {
      // Rate limiting per matching
      if (!matchRateLimiter.isAllowed(socket.id)) {
        socket.emit('videochat-error', 'Troppi tentativi di matching, attendi un momento');
        return;
      }

      const user = await userManager.getUser(socket.id);
      if (!user) {
        socket.emit('videochat-error', 'Utente non autenticato');
        return;
      }

    // Verifica se il servizio è ancora gratuito (fino a 10.000 utenti)
    const totalUsers = await User.countDocuments();
    if (totalUsers >= 10000 && !user.freeUser) {
      socket.emit('videochat-error', 'Servizio a pagamento - Contatta <EMAIL> per donazioni');
      return;
    }

    // Cerca un partner disponibile usando il nuovo sistema
    const allUsers = await userManager.getAllUsers();
    const availableUsers = allUsers.filter(({socketId}) => socketId !== socket.id);

    if (availableUsers.length === 0) {
      socket.emit('no-partners', 'Nessun partner disponibile al momento');
      return;
    }

    // Seleziona un partner casuale
    const randomIndex = Math.floor(Math.random() * availableUsers.length);
    const { socketId: partnerSocketId, userData: partnerUser } = availableUsers[randomIndex];

    // Crea una stanza di chat con il nuovo roomManager
    const roomId = `room_${Date.now()}`;
    await roomManager.addRoom(roomId, {
      users: [socket.id, partnerSocketId],
      createdAt: new Date()
    });

    // Track analytics per match riuscito
    analytics.trackEvent('chat_started', {
      roomId,
      user1: user._id.toString(),
      user2: partnerUser._id.toString()
    });

    // Unisci entrambi gli utenti alla stanza
    socket.join(roomId);
    io.sockets.sockets.get(partnerSocketId)?.join(roomId);

    // Notifica entrambi gli utenti
    socket.emit('videochat-matched', {
      roomId,
      partnerId: partnerUser._id,
      partnerEmail: partnerUser.email
    });

    io.to(partnerSocketId).emit('videochat-matched', {
      roomId,
      partnerId: user._id,
      partnerEmail: user.email
    });

    // Non è più necessario decrementare crediti - servizio gratuito
    logger.info(`Match creato tra ${user.email} e ${partnerUser.email}`);
    } catch (error) {
      logger.error('Errore durante il matching:', error);
      socket.emit('videochat-error', 'Errore durante la ricerca partner');
    }
  });

  // Segnali WebRTC (con validazione e rate limiting)
  socket.on('webrtc-offer', (data) => {
    try {
      if (!webrtcRateLimiter.isAllowed(socket.id) || !isValidWebRTCData(data)) {
        return;
      }
      logger.info('Ricevuto offer per room:', data.roomId);
      socket.to(data.roomId).emit('webrtc-offer', {
        offer: data.offer,
        from: socket.id
      });
    } catch (error) {
      logger.error('Errore WebRTC offer:', error);
    }
  });

  socket.on('webrtc-answer', (data) => {
    try {
      if (!webrtcRateLimiter.isAllowed(socket.id) || !isValidWebRTCData(data)) {
        return;
      }
      logger.info('Ricevuto answer per room:', data.roomId);
      socket.to(data.roomId).emit('webrtc-answer', {
        answer: data.answer,
        from: socket.id
      });
    } catch (error) {
      logger.error('Errore WebRTC answer:', error);
    }
  });

  socket.on('webrtc-ice-candidate', (data) => {
    try {
      if (!webrtcRateLimiter.isAllowed(socket.id) || !isValidWebRTCData(data)) {
        return;
      }
      logger.info('Ricevuto ICE candidate per room:', data.roomId);
      socket.to(data.roomId).emit('webrtc-ice-candidate', {
        candidate: data.candidate,
        from: socket.id
      });
    } catch (error) {
      logger.error('Errore WebRTC ICE candidate:', error);
    }
  });

  // Messaggi di chat (con sanitizzazione e rate limiting)
  socket.on('chat-message', async (data) => {
    try {
      // Rate limiting per messaggi
      if (!chatRateLimiter.isAllowed(socket.id)) {
        socket.emit('chat-error', 'Troppi messaggi, rallenta un po\'');
        return;
      }

      // Validazione base
      if (!data || !data.message || !data.roomId) {
        return;
      }

      // Validazione room ID
      if (!isValidRoomId(data.roomId)) {
        return;
      }

      // Sanitizzazione messaggio
      const sanitizedMessage = sanitizeChatMessage(data.message);
      if (!sanitizedMessage) {
        return;
      }

      // Verifica che l'utente sia nella stanza
      const room = await roomManager.getRoom(data.roomId);
      if (!room || !room.users.includes(socket.id)) {
        return;
      }

      // Invia messaggio sanitizzato
      socket.to(data.roomId).emit('chat-message', {
        message: sanitizedMessage,
        from: socket.userId,
        timestamp: new Date()
      });
    } catch (error) {
      logger.error('Errore gestione messaggio chat:', error);
    }
  });

  // Disconnessione
  socket.on('disconnect', async () => {
    logger.info('Utente disconnesso:', socket.id);

    const user = await userManager.getUser(socket.id);
    if (user) {
      user.isOnline = false;
      await user.save();
      await userManager.removeUser(socket.id);

      // Track analytics disconnessione
      analytics.trackEvent('user_disconnected', {
        userId: user._id.toString(),
        socketId: socket.id
      });

      // Notifica agli altri utenti
      socket.broadcast.emit('user-offline', {
        userId: user._id,
        email: user.email
      });
    }

    // Rimuovi dalle stanze di chat (controlla sia Redis che memoria)
    for (const [roomId, room] of chatRooms.entries()) {
      if (room.users.includes(socket.id)) {
        const otherUser = room.users.find(id => id !== socket.id);
        if (otherUser) {
          io.to(otherUser).emit('partner-disconnected');
        }
        await roomManager.removeRoom(roomId);
      }
    }
  });
});

// Catch-all route per SPA
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../public', 'index.html'));
});

// Avvio del server
const PORT = process.env.PORT || 3001;

server.listen(PORT, '0.0.0.0', () => {
  logger.info(`🚀 VideoChat Couple server avviato sulla porta ${PORT}`);
  logger.info(`🌐 Sito accessibile su: https://videochatcouple.com`);
  logger.info(`🔒 Sicurezza: Password hashate con bcrypt`);
  logger.info(`💾 Database: MongoDB Atlas connesso`);
  logger.info(`🎯 Pronto per il lancio!`);
  logger.info('🔗 Server HTTP interno per proxy Nginx');
  logger.info('✅ HTTPS gestito da Nginx con certificati SSL');
});

module.exports = { app, server, io };

