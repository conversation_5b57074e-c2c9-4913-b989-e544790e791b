/**
 * Test rapido del sistema email
 */

require('dotenv').config();
const emailService = require('../config/email');

async function testEmail() {
    console.log('🧪 TEST EMAIL SERVICE\n');
    
    try {
        // Verifica connessione
        console.log('🔌 Verifica connessione email...');
        const verification = await emailService.verifyConnection();
        console.log('Risultato verifica:', verification);
        
        if (verification.success) {
            console.log('\n📧 Invio email di test...');
            const result = await emailService.sendWelcomeEmail('<EMAIL>', 'Test User');
            console.log('Risultato invio:', result);
            
            if (result.success) {
                console.log('✅ Email inviata con successo!');
                console.log('Message ID:', result.messageId);
            } else {
                console.log('❌ Errore invio email:', result.error);
            }
        } else {
            console.log('❌ Connessione email fallita:', verification.error);
        }
        
    } catch (error) {
        console.error('💥 Errore durante il test:', error);
    }
}

if (require.main === module) {
    testEmail()
        .then(() => {
            console.log('\n🎯 Test completato');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n💥 Test fallito:', error);
            process.exit(1);
        });
}

module.exports = testEmail;
