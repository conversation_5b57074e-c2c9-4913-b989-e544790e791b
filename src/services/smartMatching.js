/**
 * Sistema di Matching Intelligente per VideoChat Couple
 * Algoritmo avanzato per abbinamenti ottimali
 */

const logger = require('../config/logger');

class SmartMatchingEngine {
    constructor() {
        this.userProfiles = new Map();
        this.matchHistory = new Map();
        this.interestCategories = {
            music: ['rock', 'pop', 'jazz', 'electronic', 'classical', 'hip-hop', 'indie'],
            movies: ['action', 'comedy', 'drama', 'horror', 'sci-fi', 'romance', 'documentary'],
            hobbies: ['gaming', 'reading', 'sports', 'cooking', 'travel', 'photography', 'art'],
            lifestyle: ['fitness', 'nature', 'technology', 'fashion', 'food', 'pets', 'cars']
        };
    }

    // Crea o aggiorna profilo utente
    updateUserProfile(userId, profileData) {
        const profile = {
            userId,
            age: profileData.age || null,
            location: profileData.location || null,
            interests: profileData.interests || [],
            languages: profileData.languages || ['it'],
            personality: profileData.personality || 'friendly',
            lookingFor: profileData.lookingFor || 'chat',
            onlineTime: new Date(),
            chatCount: this.userProfiles.get(userId)?.chatCount || 0,
            rating: this.userProfiles.get(userId)?.rating || 5.0,
            preferences: {
                ageRange: profileData.ageRange || { min: 18, max: 99 },
                maxDistance: profileData.maxDistance || 1000,
                languages: profileData.preferredLanguages || ['it'],
                interests: profileData.preferredInterests || []
            }
        };

        this.userProfiles.set(userId, profile);
        logger.info(`Profilo aggiornato per utente ${userId}`);
        return profile;
    }

    // Calcola compatibilità tra due utenti (0-100)
    calculateCompatibility(user1Id, user2Id) {
        const profile1 = this.userProfiles.get(user1Id);
        const profile2 = this.userProfiles.get(user2Id);

        if (!profile1 || !profile2) return 0;

        let compatibility = 0;
        let factors = 0;

        // 1. Interessi comuni (peso: 40%)
        const commonInterests = this.getCommonInterests(profile1.interests, profile2.interests);
        const interestScore = (commonInterests.length / Math.max(profile1.interests.length, profile2.interests.length, 1)) * 40;
        compatibility += interestScore;
        factors++;

        // 2. Età compatibile (peso: 20%)
        if (profile1.age && profile2.age) {
            const ageDiff = Math.abs(profile1.age - profile2.age);
            const ageScore = Math.max(0, (20 - ageDiff) / 20) * 20;
            compatibility += ageScore;
            factors++;
        }

        // 3. Lingue comuni (peso: 15%)
        const commonLanguages = profile1.languages.filter(lang => profile2.languages.includes(lang));
        const languageScore = (commonLanguages.length / Math.max(profile1.languages.length, profile2.languages.length, 1)) * 15;
        compatibility += languageScore;
        factors++;

        // 4. Personalità compatibile (peso: 10%)
        const personalityScore = this.getPersonalityCompatibility(profile1.personality, profile2.personality) * 10;
        compatibility += personalityScore;
        factors++;

        // 5. Obiettivi simili (peso: 10%)
        const goalScore = (profile1.lookingFor === profile2.lookingFor ? 1 : 0.5) * 10;
        compatibility += goalScore;
        factors++;

        // 6. Rating utenti (peso: 5%)
        const avgRating = (profile1.rating + profile2.rating) / 2;
        const ratingScore = (avgRating / 5) * 5;
        compatibility += ratingScore;
        factors++;

        return Math.round(compatibility / factors * 100) / 100;
    }

    // Trova il miglior match per un utente
    findBestMatch(userId, availableUsers) {
        const userProfile = this.userProfiles.get(userId);
        if (!userProfile) return null;

        let bestMatch = null;
        let bestScore = 0;

        for (const candidateId of availableUsers) {
            if (candidateId === userId) continue;

            // Controlla se già chattato di recente
            if (this.hasRecentChat(userId, candidateId)) continue;

            // Controlla preferenze di età
            const candidateProfile = this.userProfiles.get(candidateId);
            if (candidateProfile && !this.meetsAgePreferences(userProfile, candidateProfile)) continue;

            const compatibility = this.calculateCompatibility(userId, candidateId);
            
            // Aggiungi fattore casualità per varietà (10%)
            const randomFactor = Math.random() * 10;
            const finalScore = compatibility + randomFactor;

            if (finalScore > bestScore) {
                bestScore = finalScore;
                bestMatch = candidateId;
            }
        }

        // Registra il match nella cronologia
        if (bestMatch) {
            this.recordMatch(userId, bestMatch, bestScore);
            logger.info(`Match trovato: ${userId} <-> ${bestMatch} (score: ${bestScore.toFixed(2)})`);
        }

        return bestMatch;
    }

    // Funzioni di supporto
    getCommonInterests(interests1, interests2) {
        return interests1.filter(interest => interests2.includes(interest));
    }

    getPersonalityCompatibility(personality1, personality2) {
        const compatibilityMatrix = {
            'friendly': { 'friendly': 1.0, 'serious': 0.7, 'funny': 0.9, 'creative': 0.8 },
            'serious': { 'friendly': 0.7, 'serious': 1.0, 'funny': 0.5, 'creative': 0.6 },
            'funny': { 'friendly': 0.9, 'serious': 0.5, 'funny': 1.0, 'creative': 0.8 },
            'creative': { 'friendly': 0.8, 'serious': 0.6, 'funny': 0.8, 'creative': 1.0 }
        };

        return compatibilityMatrix[personality1]?.[personality2] || 0.5;
    }

    meetsAgePreferences(profile1, profile2) {
        if (!profile1.age || !profile2.age) return true;

        const user1InRange = profile2.age >= profile1.preferences.ageRange.min && 
                            profile2.age <= profile1.preferences.ageRange.max;
        const user2InRange = profile1.age >= profile2.preferences.ageRange.min && 
                            profile1.age <= profile2.preferences.ageRange.max;

        return user1InRange && user2InRange;
    }

    hasRecentChat(userId1, userId2) {
        const key = [userId1, userId2].sort().join('-');
        const lastChat = this.matchHistory.get(key);
        
        if (!lastChat) return false;
        
        // Evita re-match per 30 minuti
        const timeDiff = Date.now() - lastChat.timestamp;
        return timeDiff < 30 * 60 * 1000;
    }

    recordMatch(userId1, userId2, score) {
        const key = [userId1, userId2].sort().join('-');
        this.matchHistory.set(key, {
            timestamp: Date.now(),
            score: score,
            users: [userId1, userId2]
        });

        // Incrementa contatore chat
        const profile1 = this.userProfiles.get(userId1);
        const profile2 = this.userProfiles.get(userId2);
        
        if (profile1) profile1.chatCount++;
        if (profile2) profile2.chatCount++;
    }

    // Aggiorna rating utente dopo una chat
    updateUserRating(userId, rating, feedback) {
        const profile = this.userProfiles.get(userId);
        if (!profile) return;

        // Media pesata del rating
        const totalChats = profile.chatCount || 1;
        profile.rating = ((profile.rating * (totalChats - 1)) + rating) / totalChats;
        
        logger.info(`Rating aggiornato per ${userId}: ${profile.rating.toFixed(2)}`);
    }

    // Ottieni statistiche matching
    getMatchingStats() {
        const totalUsers = this.userProfiles.size;
        const totalMatches = this.matchHistory.size;
        const avgRating = Array.from(this.userProfiles.values())
            .reduce((sum, profile) => sum + profile.rating, 0) / totalUsers || 0;

        return {
            totalUsers,
            totalMatches,
            avgRating: Math.round(avgRating * 100) / 100,
            activeUsers: Array.from(this.userProfiles.values())
                .filter(profile => Date.now() - profile.onlineTime.getTime() < 5 * 60 * 1000).length
        };
    }

    // Cleanup periodico
    cleanup() {
        const now = Date.now();
        const oneHourAgo = now - 60 * 60 * 1000;

        // Rimuovi cronologia vecchia
        for (const [key, match] of this.matchHistory.entries()) {
            if (match.timestamp < oneHourAgo) {
                this.matchHistory.delete(key);
            }
        }

        // Rimuovi profili inattivi (oltre 24 ore)
        const oneDayAgo = now - 24 * 60 * 60 * 1000;
        for (const [userId, profile] of this.userProfiles.entries()) {
            if (profile.onlineTime.getTime() < oneDayAgo) {
                this.userProfiles.delete(userId);
            }
        }

        logger.info('Cleanup matching engine completato');
    }
}

// Istanza singleton
const smartMatching = new SmartMatchingEngine();

// Cleanup ogni ora
setInterval(() => {
    smartMatching.cleanup();
}, 60 * 60 * 1000);

module.exports = smartMatching;
