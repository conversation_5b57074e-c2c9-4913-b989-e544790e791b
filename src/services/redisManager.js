/**
 * Redis Manager con fallback sicuro
 * Se Redis non è disponibile, usa memoria locale senza rompere il servizio
 */

const Redis = require('ioredis');
const logger = require('../config/logger');

class RedisManager {
  constructor() {
    this.redis = null;
    this.isConnected = false;
    this.fallbackStorage = new Map(); // Fallback in memoria
    this.init();
  }

  async init() {
    try {
      this.redis = new Redis({
        host: process.env.REDIS_HOST || 'localhost',
        port: process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD || '',
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true
      });

      // Test connessione
      await this.redis.ping();
      this.isConnected = true;
      logger.info('✅ Redis connesso e attivo');

      // Gestione eventi
      this.redis.on('error', (err) => {
        logger.warn('⚠️ Redis errore, usando fallback memoria:', err.message);
        this.isConnected = false;
      });

      this.redis.on('connect', () => {
        logger.info('🔄 Redis riconnesso');
        this.isConnected = true;
      });

    } catch (error) {
      logger.warn('⚠️ Redis non disponibile, usando memoria locale:', error.message);
      this.isConnected = false;
    }
  }

  // Metodi sicuri con fallback automatico
  async setUser(socketId, userData) {
    try {
      if (this.isConnected && this.redis) {
        await this.redis.setex(`user:${socketId}`, 3600, JSON.stringify(userData));
        return true;
      }
    } catch (error) {
      logger.warn('Redis setUser fallback:', error.message);
    }
    
    // Fallback memoria
    this.fallbackStorage.set(`user:${socketId}`, userData);
    return true;
  }

  async getUser(socketId) {
    try {
      if (this.isConnected && this.redis) {
        const data = await this.redis.get(`user:${socketId}`);
        return data ? JSON.parse(data) : null;
      }
    } catch (error) {
      logger.warn('Redis getUser fallback:', error.message);
    }
    
    // Fallback memoria
    return this.fallbackStorage.get(`user:${socketId}`) || null;
  }

  async removeUser(socketId) {
    try {
      if (this.isConnected && this.redis) {
        await this.redis.del(`user:${socketId}`);
      }
    } catch (error) {
      logger.warn('Redis removeUser fallback:', error.message);
    }
    
    // Fallback memoria
    this.fallbackStorage.delete(`user:${socketId}`);
  }

  async setRoom(roomId, roomData) {
    try {
      if (this.isConnected && this.redis) {
        await this.redis.setex(`room:${roomId}`, 7200, JSON.stringify(roomData)); // 2 ore
        return true;
      }
    } catch (error) {
      logger.warn('Redis setRoom fallback:', error.message);
    }
    
    // Fallback memoria
    this.fallbackStorage.set(`room:${roomId}`, roomData);
    return true;
  }

  async getRoom(roomId) {
    try {
      if (this.isConnected && this.redis) {
        const data = await this.redis.get(`room:${roomId}`);
        return data ? JSON.parse(data) : null;
      }
    } catch (error) {
      logger.warn('Redis getRoom fallback:', error.message);
    }
    
    // Fallback memoria
    return this.fallbackStorage.get(`room:${roomId}`) || null;
  }

  async removeRoom(roomId) {
    try {
      if (this.isConnected && this.redis) {
        await this.redis.del(`room:${roomId}`);
      }
    } catch (error) {
      logger.warn('Redis removeRoom fallback:', error.message);
    }
    
    // Fallback memoria
    this.fallbackStorage.delete(`room:${roomId}`);
  }

  async getAllActiveUsers() {
    try {
      if (this.isConnected && this.redis) {
        const keys = await this.redis.keys('user:*');
        const users = [];
        for (const key of keys) {
          const data = await this.redis.get(key);
          if (data) {
            users.push({
              socketId: key.replace('user:', ''),
              userData: JSON.parse(data)
            });
          }
        }
        return users;
      }
    } catch (error) {
      logger.warn('Redis getAllActiveUsers fallback:', error.message);
    }
    
    // Fallback memoria
    const users = [];
    for (const [key, userData] of this.fallbackStorage.entries()) {
      if (key.startsWith('user:')) {
        users.push({
          socketId: key.replace('user:', ''),
          userData
        });
      }
    }
    return users;
  }

  // Cleanup periodico per fallback memoria
  startCleanup() {
    setInterval(() => {
      if (!this.isConnected) {
        // Cleanup memoria ogni 10 minuti se Redis non è disponibile
        const now = Date.now();
        for (const [key, data] of this.fallbackStorage.entries()) {
          if (key.startsWith('room:') && data.createdAt) {
            // Rimuovi stanze più vecchie di 2 ore
            if (now - new Date(data.createdAt).getTime() > 2 * 60 * 60 * 1000) {
              this.fallbackStorage.delete(key);
            }
          }
        }
      }
    }, 10 * 60 * 1000); // 10 minuti
  }

  // Statistiche per monitoring
  getStats() {
    return {
      redisConnected: this.isConnected,
      fallbackEntries: this.fallbackStorage.size,
      storageType: this.isConnected ? 'Redis' : 'Memory'
    };
  }
}

// Singleton instance
const redisManager = new RedisManager();
redisManager.startCleanup();

module.exports = redisManager;
