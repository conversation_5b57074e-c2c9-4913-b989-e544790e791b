/**
 * Sistema di Feedback e Rating per VideoChat Couple
 * Migliora la qualità delle chat attraverso feedback degli utenti
 */

const logger = require('../config/logger');
const User = require('../models/user.model');

class FeedbackSystem {
    constructor() {
        this.pendingFeedbacks = new Map(); // roomId -> feedback data
        this.userStats = new Map(); // userId -> stats
    }

    // Richiedi feedback alla fine di una chat
    requestFeedback(roomId, userId1, userId2, chatDuration) {
        const feedbackData = {
            roomId,
            users: [userId1, userId2],
            chatDuration,
            startTime: Date.now(),
            feedbacks: new Map()
        };

        this.pendingFeedbacks.set(roomId, feedbackData);
        
        // Auto-cleanup dopo 5 minuti se nessun feedback
        setTimeout(() => {
            if (this.pendingFeedbacks.has(roomId)) {
                this.pendingFeedbacks.delete(roomId);
                logger.info(`Feedback timeout per room ${roomId}`);
            }
        }, 5 * 60 * 1000);

        logger.info(`Feedback richiesto per room ${roomId}, durata: ${chatDuration}s`);
        return feedbackData;
    }

    // Ricevi feedback da un utente
    async submitFeedback(roomId, fromUserId, feedback) {
        const pendingFeedback = this.pendingFeedbacks.get(roomId);
        if (!pendingFeedback) {
            return { success: false, message: 'Sessione feedback scaduta' };
        }

        if (!pendingFeedback.users.includes(fromUserId)) {
            return { success: false, message: 'Utente non autorizzato' };
        }

        // Valida feedback
        const validatedFeedback = this.validateFeedback(feedback);
        if (!validatedFeedback.valid) {
            return { success: false, message: validatedFeedback.error };
        }

        // Salva feedback
        pendingFeedback.feedbacks.set(fromUserId, {
            rating: feedback.rating,
            tags: feedback.tags || [],
            comment: feedback.comment || '',
            timestamp: Date.now()
        });

        // Se entrambi hanno dato feedback, processa
        if (pendingFeedback.feedbacks.size === 2) {
            await this.processFeedbacks(pendingFeedback);
            this.pendingFeedbacks.delete(roomId);
        }

        logger.info(`Feedback ricevuto da ${fromUserId} per room ${roomId}`);
        return { success: true, message: 'Feedback salvato' };
    }

    // Valida i dati del feedback
    validateFeedback(feedback) {
        if (!feedback.rating || feedback.rating < 1 || feedback.rating > 5) {
            return { valid: false, error: 'Rating deve essere tra 1 e 5' };
        }

        if (feedback.comment && feedback.comment.length > 500) {
            return { valid: false, error: 'Commento troppo lungo (max 500 caratteri)' };
        }

        const validTags = [
            'friendly', 'funny', 'interesting', 'respectful', 'creative',
            'rude', 'boring', 'inappropriate', 'spam', 'fake'
        ];

        if (feedback.tags && !Array.isArray(feedback.tags)) {
            return { valid: false, error: 'Tags devono essere un array' };
        }

        if (feedback.tags) {
            for (const tag of feedback.tags) {
                if (!validTags.includes(tag)) {
                    return { valid: false, error: `Tag non valido: ${tag}` };
                }
            }
        }

        return { valid: true };
    }

    // Processa i feedback di entrambi gli utenti
    async processFeedbacks(feedbackData) {
        const [userId1, userId2] = feedbackData.users;
        const feedback1 = feedbackData.feedbacks.get(userId1);
        const feedback2 = feedbackData.feedbacks.get(userId2);

        try {
            // Aggiorna rating degli utenti
            if (feedback1) {
                await this.updateUserRating(userId2, feedback1.rating, feedback1.tags);
            }
            if (feedback2) {
                await this.updateUserRating(userId1, feedback2.rating, feedback2.tags);
            }

            // Aggiorna statistiche
            await this.updateChatStats(userId1, userId2, feedbackData.chatDuration);

            // Controlla se ci sono problemi da segnalare
            await this.checkForIssues(userId1, userId2, feedback1, feedback2);

            logger.info(`Feedback processato per users ${userId1}, ${userId2}`);
        } catch (error) {
            logger.error('Errore processing feedback:', error);
        }
    }

    // Aggiorna rating utente
    async updateUserRating(userId, rating, tags) {
        try {
            const user = await User.findById(userId);
            if (!user) return;

            // Calcola nuovo rating (media pesata)
            const totalChats = user.stats.totalChats || 1;
            const currentRating = user.stats.rating || 5.0;
            const newRating = ((currentRating * totalChats) + rating) / (totalChats + 1);

            // Aggiorna stats
            user.stats.rating = Math.round(newRating * 100) / 100;
            user.stats.totalRatings = (user.stats.totalRatings || 0) + 1;

            // Processa tags
            if (tags && tags.length > 0) {
                user.stats.tags = user.stats.tags || {};
                tags.forEach(tag => {
                    user.stats.tags[tag] = (user.stats.tags[tag] || 0) + 1;
                });
            }

            await user.save();
            logger.info(`Rating aggiornato per ${userId}: ${newRating.toFixed(2)}`);
        } catch (error) {
            logger.error(`Errore aggiornamento rating per ${userId}:`, error);
        }
    }

    // Aggiorna statistiche chat
    async updateChatStats(userId1, userId2, duration) {
        try {
            const users = await User.find({ _id: { $in: [userId1, userId2] } });
            
            for (const user of users) {
                user.stats.totalChats = (user.stats.totalChats || 0) + 1;
                user.stats.totalMinutes = (user.stats.totalMinutes || 0) + Math.round(duration / 60);
                user.stats.lastChatAt = new Date();
                await user.save();
            }
        } catch (error) {
            logger.error('Errore aggiornamento stats chat:', error);
        }
    }

    // Controlla problemi e comportamenti inappropriati
    async checkForIssues(userId1, userId2, feedback1, feedback2) {
        const lowRatingThreshold = 2;
        const problematicTags = ['rude', 'inappropriate', 'spam', 'fake'];

        // Controlla rating bassi
        if (feedback1 && feedback1.rating <= lowRatingThreshold) {
            await this.flagUser(userId2, 'low_rating', feedback1);
        }
        if (feedback2 && feedback2.rating <= lowRatingThreshold) {
            await this.flagUser(userId1, 'low_rating', feedback2);
        }

        // Controlla tag problematici
        if (feedback1 && feedback1.tags.some(tag => problematicTags.includes(tag))) {
            await this.flagUser(userId2, 'problematic_behavior', feedback1);
        }
        if (feedback2 && feedback2.tags.some(tag => problematicTags.includes(tag))) {
            await this.flagUser(userId1, 'problematic_behavior', feedback2);
        }
    }

    // Segnala utente problematico
    async flagUser(userId, reason, feedback) {
        try {
            const user = await User.findById(userId);
            if (!user) return;

            user.stats.flags = user.stats.flags || [];
            user.stats.flags.push({
                reason,
                timestamp: new Date(),
                rating: feedback.rating,
                tags: feedback.tags,
                comment: feedback.comment
            });

            // Se troppi flag, sospendi temporaneamente
            const recentFlags = user.stats.flags.filter(
                flag => Date.now() - flag.timestamp.getTime() < 24 * 60 * 60 * 1000
            );

            if (recentFlags.length >= 3) {
                user.status = 'suspended';
                user.suspendedAt = new Date();
                user.suspendedReason = 'Troppi feedback negativi';
                
                logger.warn(`Utente ${userId} sospeso per troppi feedback negativi`);
            }

            await user.save();
        } catch (error) {
            logger.error(`Errore flagging user ${userId}:`, error);
        }
    }

    // Ottieni statistiche feedback
    getFeedbackStats() {
        const totalPending = this.pendingFeedbacks.size;
        const totalProcessed = Array.from(this.pendingFeedbacks.values())
            .filter(feedback => feedback.feedbacks.size === 2).length;

        return {
            pendingFeedbacks: totalPending,
            processedFeedbacks: totalProcessed,
            timestamp: new Date().toISOString()
        };
    }

    // Cleanup periodico
    cleanup() {
        const now = Date.now();
        const expiredTime = 10 * 60 * 1000; // 10 minuti

        for (const [roomId, feedback] of this.pendingFeedbacks.entries()) {
            if (now - feedback.startTime > expiredTime) {
                this.pendingFeedbacks.delete(roomId);
            }
        }

        logger.info('Feedback system cleanup completato');
    }
}

// Istanza singleton
const feedbackSystem = new FeedbackSystem();

// Cleanup ogni 15 minuti
setInterval(() => {
    feedbackSystem.cleanup();
}, 15 * 60 * 1000);

module.exports = feedbackSystem;
