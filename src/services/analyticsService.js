/**
 * Sistema di Analytics in Tempo Reale per VideoChat Couple
 * Raccoglie e analizza dati di utilizzo per migliorare il servizio
 */

const logger = require('../config/logger');
const redisManager = require('./redisManager');

class AnalyticsService {
    constructor() {
        this.metrics = new Map();
        this.dailyStats = new Map();
        this.realTimeStats = {
            activeUsers: 0,
            activeChats: 0,
            totalConnections: 0,
            avgChatDuration: 0,
            peakUsers: 0,
            errors: 0
        };
        
        this.startTime = Date.now();
        this.initializeMetrics();
    }

    initializeMetrics() {
        // Inizializza metriche base
        this.metrics.set('user_connections', 0);
        this.metrics.set('chat_sessions', 0);
        this.metrics.set('successful_matches', 0);
        this.metrics.set('failed_matches', 0);
        this.metrics.set('total_chat_minutes', 0);
        this.metrics.set('user_registrations', 0);
        this.metrics.set('errors_count', 0);
        
        logger.info('Analytics service inizializzato');
    }

    // Traccia evento
    trackEvent(eventType, data = {}) {
        const timestamp = Date.now();
        const event = {
            type: eventType,
            timestamp,
            data,
            date: new Date().toISOString().split('T')[0] // YYYY-MM-DD
        };

        this.processEvent(event);
        this.updateRealTimeStats(event);
        this.updateDailyStats(event);

        // Salva in Redis se disponibile
        this.saveToRedis(event);
    }

    processEvent(event) {
        switch (event.type) {
            case 'user_connected':
                this.incrementMetric('user_connections');
                this.realTimeStats.activeUsers++;
                this.updatePeakUsers();
                break;

            case 'user_disconnected':
                this.realTimeStats.activeUsers = Math.max(0, this.realTimeStats.activeUsers - 1);
                break;

            case 'chat_started':
                this.incrementMetric('chat_sessions');
                this.incrementMetric('successful_matches');
                this.realTimeStats.activeChats++;
                break;

            case 'chat_ended':
                this.realTimeStats.activeChats = Math.max(0, this.realTimeStats.activeChats - 1);
                if (event.data.duration) {
                    this.addChatDuration(event.data.duration);
                }
                break;

            case 'match_failed':
                this.incrementMetric('failed_matches');
                break;

            case 'user_registered':
                this.incrementMetric('user_registrations');
                break;

            case 'error':
                this.incrementMetric('errors_count');
                this.realTimeStats.errors++;
                break;

            case 'webrtc_connection':
                this.incrementMetric('webrtc_connections');
                break;

            case 'message_sent':
                this.incrementMetric('messages_sent');
                break;
        }
    }

    updateRealTimeStats(event) {
        this.realTimeStats.totalConnections = this.getMetric('user_connections');
        
        // Calcola durata media chat
        const totalMinutes = this.getMetric('total_chat_minutes');
        const totalChats = this.getMetric('chat_sessions');
        this.realTimeStats.avgChatDuration = totalChats > 0 ? 
            Math.round((totalMinutes / totalChats) * 100) / 100 : 0;
    }

    updateDailyStats(event) {
        const date = event.date;
        if (!this.dailyStats.has(date)) {
            this.dailyStats.set(date, {
                date,
                users: new Set(),
                chats: 0,
                registrations: 0,
                totalDuration: 0,
                errors: 0,
                peakUsers: 0
            });
        }

        const dayStats = this.dailyStats.get(date);

        switch (event.type) {
            case 'user_connected':
                if (event.data.userId) {
                    dayStats.users.add(event.data.userId);
                }
                break;

            case 'chat_started':
                dayStats.chats++;
                break;

            case 'chat_ended':
                if (event.data.duration) {
                    dayStats.totalDuration += event.data.duration;
                }
                break;

            case 'user_registered':
                dayStats.registrations++;
                break;

            case 'error':
                dayStats.errors++;
                break;
        }

        // Aggiorna picco utenti giornaliero
        dayStats.peakUsers = Math.max(dayStats.peakUsers, this.realTimeStats.activeUsers);
    }

    // Metodi di utilità
    incrementMetric(key, value = 1) {
        const current = this.metrics.get(key) || 0;
        this.metrics.set(key, current + value);
    }

    getMetric(key) {
        return this.metrics.get(key) || 0;
    }

    addChatDuration(duration) {
        const minutes = Math.round(duration / 60);
        this.incrementMetric('total_chat_minutes', minutes);
    }

    updatePeakUsers() {
        this.realTimeStats.peakUsers = Math.max(
            this.realTimeStats.peakUsers, 
            this.realTimeStats.activeUsers
        );
    }

    // Salva evento in Redis per persistenza
    async saveToRedis(event) {
        try {
            const key = `analytics:events:${event.date}`;
            await redisManager.redis?.lpush(key, JSON.stringify(event));
            await redisManager.redis?.expire(key, 7 * 24 * 60 * 60); // 7 giorni
        } catch (error) {
            // Fallback silenzioso se Redis non disponibile
        }
    }

    // Ottieni statistiche complete
    getStats() {
        const uptime = Math.round((Date.now() - this.startTime) / 1000);
        const today = new Date().toISOString().split('T')[0];
        const todayStats = this.dailyStats.get(today);

        return {
            realTime: {
                ...this.realTimeStats,
                uptime
            },
            today: todayStats ? {
                uniqueUsers: todayStats.users.size,
                totalChats: todayStats.chats,
                newRegistrations: todayStats.registrations,
                totalChatMinutes: Math.round(todayStats.totalDuration / 60),
                errors: todayStats.errors,
                peakUsers: todayStats.peakUsers
            } : null,
            allTime: {
                totalConnections: this.getMetric('user_connections'),
                totalChats: this.getMetric('chat_sessions'),
                successfulMatches: this.getMetric('successful_matches'),
                failedMatches: this.getMetric('failed_matches'),
                totalChatMinutes: this.getMetric('total_chat_minutes'),
                totalRegistrations: this.getMetric('user_registrations'),
                totalErrors: this.getMetric('errors_count'),
                successRate: this.calculateSuccessRate()
            },
            performance: {
                avgChatDuration: this.realTimeStats.avgChatDuration,
                matchSuccessRate: this.calculateMatchSuccessRate(),
                errorRate: this.calculateErrorRate(),
                userRetention: this.calculateUserRetention()
            }
        };
    }

    // Calcoli metriche
    calculateSuccessRate() {
        const successful = this.getMetric('successful_matches');
        const failed = this.getMetric('failed_matches');
        const total = successful + failed;
        return total > 0 ? Math.round((successful / total) * 100) : 0;
    }

    calculateMatchSuccessRate() {
        const connections = this.getMetric('user_connections');
        const matches = this.getMetric('successful_matches');
        return connections > 0 ? Math.round((matches / connections) * 100) : 0;
    }

    calculateErrorRate() {
        const errors = this.getMetric('errors_count');
        const connections = this.getMetric('user_connections');
        return connections > 0 ? Math.round((errors / connections) * 100) : 0;
    }

    calculateUserRetention() {
        // Calcolo semplificato basato su utenti che tornano
        const today = new Date().toISOString().split('T')[0];
        const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        
        const todayUsers = this.dailyStats.get(today)?.users || new Set();
        const yesterdayUsers = this.dailyStats.get(yesterday)?.users || new Set();
        
        if (yesterdayUsers.size === 0) return 0;
        
        const returningUsers = Array.from(todayUsers).filter(user => yesterdayUsers.has(user));
        return Math.round((returningUsers.length / yesterdayUsers.size) * 100);
    }

    // Ottieni trend degli ultimi giorni
    getTrends(days = 7) {
        const trends = [];
        const now = new Date();
        
        for (let i = days - 1; i >= 0; i--) {
            const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
            const dateStr = date.toISOString().split('T')[0];
            const dayStats = this.dailyStats.get(dateStr);
            
            trends.push({
                date: dateStr,
                users: dayStats?.users.size || 0,
                chats: dayStats?.chats || 0,
                registrations: dayStats?.registrations || 0,
                errors: dayStats?.errors || 0,
                peakUsers: dayStats?.peakUsers || 0
            });
        }
        
        return trends;
    }

    // Cleanup dati vecchi
    cleanup() {
        const cutoffDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 giorni
        const cutoffStr = cutoffDate.toISOString().split('T')[0];
        
        for (const [date] of this.dailyStats.entries()) {
            if (date < cutoffStr) {
                this.dailyStats.delete(date);
            }
        }
        
        logger.info('Analytics cleanup completato');
    }
}

// Istanza singleton
const analytics = new AnalyticsService();

// Cleanup giornaliero
setInterval(() => {
    analytics.cleanup();
}, 24 * 60 * 60 * 1000);

module.exports = analytics;
