{"timestamp": "2025-06-13T17:59:24.152Z", "maxConcurrentUsers": 50, "summary": {"recommendedCapacity": 35, "safeCapacity": 25, "peakCapacity": 50}, "performance": {"avgConnectionTime": 479, "avgResponseTime": 4, "memoryPerUser": "0.05", "successRate": "100.00%"}, "details": {"maxConcurrentUsers": 50, "connectionTime": [{"users": 5, "time": 262, "avgPerUser": 52.4}, {"users": 10, "time": 233, "avgPerUser": 23.3}, {"users": 15, "time": 226, "avgPerUser": 15.066666666666666}, {"users": 20, "time": 313, "avgPerUser": 15.65}, {"users": 25, "time": 498, "avgPerUser": 19.92}, {"users": 30, "time": 576, "avgPerUser": 19.2}, {"users": 35, "time": 492, "avgPerUser": 14.057142857142857}, {"users": 40, "time": 697, "avgPerUser": 17.425}, {"users": 45, "time": 747, "avgPerUser": 16.6}, {"users": 50, "time": 741, "avgPerUser": 14.82}], "memoryUsage": [{"users": 5, "heapUsed": -1.2146453857421875, "rss": 2.515625}, {"users": 10, "heapUsed": 1.2934722900390625, "rss": 0.75}, {"users": 15, "heapUsed": 1.7842788696289062, "rss": 0.625}, {"users": 20, "heapUsed": -4.0345001220703125, "rss": 0.75}, {"users": 25, "heapUsed": 2.6027679443359375, "rss": 3.25}, {"users": 30, "heapUsed": 0.9944381713867188, "rss": 1.640625}, {"users": 35, "heapUsed": -0.38802337646484375, "rss": 2.25}, {"users": 40, "heapUsed": -0.30458831787109375, "rss": 2.875}, {"users": 45, "heapUsed": 1.7584304809570312, "rss": 2.375}, {"users": 50, "heapUsed": 2.3109130859375, "rss": 0.5}], "responseTime": [{"users": 5, "avgResponse": 7.4}, {"users": 10, "avgResponse": 3}, {"users": 15, "avgResponse": 2.3}, {"users": 20, "avgResponse": 3.4}, {"users": 25, "avgResponse": 3.8}, {"users": 30, "avgResponse": 3}, {"users": 35, "avgResponse": 3.8}, {"users": 40, "avgResponse": 6.5}, {"users": 45, "avgResponse": 2.3}, {"users": 50, "avgResponse": 1.8}], "errors": [], "successfulConnections": 50, "failedConnections": 0}}